3. Diagnostic Services in Clinic details: Remove Status and make it editable -> Client
4. Manage Users -> Remove & Change role CTAs alignment with icons

- Also integrate below APIs, error message show from BE
  - Change Role: pas corect data + id token in admin
  - Remove: pass query params in Delete api

11. Manage admins: Integrate remove admin & add admin popup
 - Remove admin: /api/admins/remove-admin payload { uid: string; }
 - add admin: /api/admins/register-admin payload { uid: string; }

12. Manage clinics in admin -> add clinic Popup
