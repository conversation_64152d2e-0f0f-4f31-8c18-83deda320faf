// clinic-form-schema.ts
import { z } from 'zod';

export const clinicFormSchema = z.object({
  clinic_name: z.string().min(2, {
    message: 'Clinic name must be at least 2 characters.',
  }),
  clinic_email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  clinic_website: z
    .string()
    .url({ message: 'Please enter a valid URL.' })
    .optional()
    .or(z.literal('')),
  clinic_phone: z.string().optional(),
  human_transfer_destination_number: z
    .string()
    .regex(/^\+\d{1,4} \d{10}$/, {
      message:
        'Please enter a valid phone number in format: +[country code] [10 digits] (e.g., *************)',
    })
    .optional()
    .or(z.literal('')),
  clinic_addresses: z
    .array(
      z.object({
        address: z.string().optional(),
        full_address: z.string().min(1, 'Full address is required'),
        business_location_id: z.string().optional(),
      }),
    )
    .optional(),
});

export type ClinicFormValues = z.infer<typeof clinicFormSchema>;
